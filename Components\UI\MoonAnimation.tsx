import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';

interface MoonAnimationProps {
  currentPhase: string; // Phase cinématographique actuelle (aube/midi/coucher/nuit)
}

const MoonAnimation: React.FC<MoonAnimationProps> = ({ currentPhase }) => {
  const moonRef = useRef<HTMLDivElement>(null);
  const haloRef = useRef<HTMLDivElement>(null); // 🔧 CISCO: Référence séparée pour le halo
  const animationRef = useRef<gsap.core.Timeline | null>(null);
  const fadeOutRef = useRef<gsap.core.Timeline | null>(null); // 🔧 CISCO: Correction type Timeline
  const isAnimatingRef = useRef<boolean>(false); // 🔧 CISCO: Protection contre les déclenchements multiples
  const hasAnimatedRef = useRef<boolean>(false); // 🔧 CISCO: Empêcher les re-animations

  // 🔧 CISCO: Références pour le cycle automatique
  const automaticCycleRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!moonRef.current || !haloRef.current) return;

    // 🔧 CISCO: DÉBOGAGE - Tracer tous les déclenchements du useEffect
    console.log(`🌙 MoonAnimation useEffect déclenché: currentPhase=${currentPhase}, isAnimating=${isAnimatingRef.current}`);

    // 🌙 CISCO: Cycle complet de la lune - Visible nuit et aube
    const isMoonVisiblePhase = currentPhase === 'nuit' || currentPhase === 'night' ||
                               currentPhase === 'aube' || currentPhase === 'dawn';

    if (isMoonVisiblePhase) {
      // 🔧 CISCO: SUPPRESSION protection hasAnimatedRef - Permettre cycle complet
      console.log(`🌙 Phase lune visible détectée: ${currentPhase}`);

      // Réinitialiser les protections pour permettre nouveau cycle
      hasAnimatedRef.current = false;

      // 🔧 CISCO: PROTECTION RENFORCÉE - Éviter les déclenchements multiples
      if (isAnimatingRef.current) {
        console.log('🌙 Animation lune déjà en cours (protection renforcée) - éviter le redémarrage');
        return;
      }

      // 🔧 CISCO: Vérifier si l'animation GSAP est déjà active
      if (animationRef.current && animationRef.current.isActive()) {
        console.log('🌙 Animation GSAP lune déjà active - éviter le redémarrage');
        return;
      }

      // 🔧 CISCO: Animation selon la phase
      if (currentPhase === 'nuit' || currentPhase === 'night') {
        console.log('🌙 DÉMARRAGE animation lune - NUIT: Lever complet');
      } else {
        console.log('🌙 DÉMARRAGE animation lune - AUBE: Coucher progressif');
      }

      isAnimatingRef.current = true; // 🔧 CISCO: Marquer comme en cours d'animation
      hasAnimatedRef.current = true; // 🔧 CISCO: Marquer comme déjà animé

      // Arrêter toute animation en cours
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }

      // 🔧 CISCO: Position initiale de la lune - DERRIÈRE LE PAYSAGE (plus bas)
      gsap.set(moonRef.current, {
        x: '5vw', // Gauche pour commencer la trajectoire parabolique
        y: '75vh', // 🔧 CISCO: DERRIÈRE PAYSAGE - Descente de 35vh à 75vh (plus bas)
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        scale: 1,
        display: 'block'
      });

      // Position initiale du halo (même position que la lune)
      gsap.set(haloRef.current, {
        x: '5vw',
        y: '75vh', // 🔧 CISCO: Même position que la lune (derrière paysage)
        xPercent: -50,
        yPercent: -50,
        opacity: 0,
        display: 'block'
      });

      // 🔧 CISCO: Timeline SANS DÉLAI - Animation immédiate
      animationRef.current = gsap.timeline({
        onComplete: () => {
          console.log('🌙 Animation lune terminée - Libération du verrou');
          isAnimatingRef.current = false; // 🔧 CISCO: Libérer le verrou à la fin
        }
      });

      // 🔧 CISCO: Animation selon la phase
      if (currentPhase === 'nuit' || currentPhase === 'night') {
        // 🌙 NUIT: Trajectoire complète lever → zénith → coucher
        gsap.set(moonRef.current, { opacity: 1.0 });
        gsap.set(haloRef.current, { opacity: 0.25 });

        animationRef.current.to([moonRef.current, haloRef.current], {
          keyframes: [
            // Phase 1: Lever depuis derrière le paysage vers le zénith
            { x: '5vw', y: '75vh', duration: 0 },     // 🔧 CISCO: Départ DERRIÈRE PAYSAGE (75vh)
            { x: '15vw', y: '55vh', duration: 0.15 }, // 🔧 CISCO: Montée progressive depuis paysage
            { x: '25vw', y: '35vh', duration: 0.25 }, // 🔧 CISCO: Continue la montée
            { x: '35vw', y: '20vh', duration: 0.35 }, // 🔧 CISCO: Approche du zénith
            { x: '45vw', y: '10vh', duration: 0.45 }, // 🔧 CISCO: Proche du zénith
            { x: '50vw', y: '5vh', duration: 0.5 },   // 🔧 CISCO: ZÉNITH ABSOLU (point le plus haut)
            // Phase 2: Coucher du zénith vers derrière paysage opposé
            { x: '55vw', y: '10vh', duration: 0.55 }, // 🔧 CISCO: Début descente depuis zénith
            { x: '65vw', y: '20vh', duration: 0.65 }, // 🔧 CISCO: Descente progressive
            { x: '75vw', y: '35vh', duration: 0.75 }, // 🔧 CISCO: Continue la descente
            { x: '85vw', y: '55vh', duration: 0.85 }, // 🔧 CISCO: Approche du paysage opposé
            { x: '95vw', y: '75vh', duration: 1.0 }   // 🔧 CISCO: COUCHER DERRIÈRE PAYSAGE OPPOSÉ
          ],
          duration: 900, // 🔧 CISCO: 15 minutes - mouvement naturel et lent
          ease: "power2.inOut", // Courbe parabolique naturelle
          transformOrigin: "center center"
        }); // 🔧 CISCO: Trajectoire complète lever→zénith→coucher

      } else if (currentPhase === 'aube' || currentPhase === 'dawn') {
        // 🌅 AUBE: Lune visible mais se couche progressivement
        gsap.set(moonRef.current, { opacity: 0.3, x: '80vw', y: '30vh' }); // Position haute à droite
        gsap.set(haloRef.current, { opacity: 0.1 });

        animationRef.current.to([moonRef.current, haloRef.current], {
          keyframes: [
            { x: '80vw', y: '30vh', duration: 0 },     // Position initiale haute
            { x: '85vw', y: '50vh', duration: 0.3 },   // Descente progressive
            { x: '90vw', y: '70vh', duration: 0.7 },   // Approche horizon
            { x: '95vw', y: '80vh', duration: 1.0 }    // Coucher derrière paysage
          ],
          duration: 300, // 🔧 CISCO: 5 minutes - Coucher progressif à l'aube
          ease: "power2.inOut",
          transformOrigin: "center center"
        });

        // Disparition progressive de l'opacité
        animationRef.current.to([moonRef.current, haloRef.current], {
          opacity: 0,
          duration: 300,
          ease: "power2.out"
        }, 0);
      }

    } else {
      // 🔧 CISCO: Autres phases (aube, midi, coucher) - Lune cachée
      const isNonNightPhase = currentPhase === 'aube' || currentPhase === 'dawn' ||
                              currentPhase === 'midi' || currentPhase === 'midday' ||
                              currentPhase === 'coucher' || currentPhase === 'sunset';

      if (isNonNightPhase) {
        console.log(`🌙 Phase ${currentPhase} détectée - Lune cachée`);

        // 🔧 CISCO: Libérer TOUS les verrous d'animation
        isAnimatingRef.current = false;
        hasAnimatedRef.current = false; // 🔧 CISCO: Permettre nouvelle animation si retour mode nuit

        // Arrêter l'animation de descente
        if (animationRef.current) {
          animationRef.current.kill();
          animationRef.current = null;
        }

        // Si la lune est visible, la faire disparaître en douceur avec le halo
        if (
          moonRef.current &&
          Number(gsap.getProperty(moonRef.current, "opacity")) > 0
        ) {
          fadeOutRef.current = gsap.timeline();

          // Disparition de la lune
          fadeOutRef.current.to(moonRef.current, {
            opacity: 0,
            duration: 8,
            ease: "power2.in"
          });

          // Disparition du halo en parallèle
          fadeOutRef.current.to(haloRef.current, {
            opacity: 0,
            duration: 8,
            ease: "power2.in",
            onComplete: () => {
              if (moonRef.current && haloRef.current) {
                gsap.set(moonRef.current, { display: 'none' });
                gsap.set(haloRef.current, { display: 'none' });
              }
            }
          }, 0); // En même temps que la lune
        } else {
          // Si déjà invisible, juste les cacher
          gsap.set(moonRef.current, { display: 'none' });
          gsap.set(haloRef.current, { display: 'none' });
        }
      }
    }

    // Nettoyage au démontage
    return () => {
      if (animationRef.current) {
        animationRef.current.kill();
      }
      if (fadeOutRef.current) {
        fadeOutRef.current.kill();
      }
    };
  }, [currentPhase]);

  return (
    <>
      {/* 🌙 CISCO: Halo lumineux séparé pour éviter l'effet carré */}
      <div
        ref={haloRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '200px',
          height: '200px',
          background: 'radial-gradient(circle, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 30%, rgba(255, 255, 255, 0.04) 60%, transparent 100%)', // 🔧 CISCO: Halo plus lumineux
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
        }}
      />

      {/* 🌙 CISCO: Lune principale */}
      <div
        ref={moonRef}
        className="fixed top-0 left-0 pointer-events-none"
        style={{
          zIndex: 8, // 🔧 CISCO: Lune + Halo derrière les nuages (z-index 8)
          display: 'none',
          width: '120px',
          height: '120px',
          backgroundImage: 'url(/Lune-Moon.png)',
          backgroundSize: 'contain',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          filter: 'brightness(1.6) contrast(1.3)', // 🔧 CISCO: Plus lumineuse (était 1.3/1.1)
        }}
        title="🌙 Lune nocturne"
      />
    </>
  );
};

export default MoonAnimation;
